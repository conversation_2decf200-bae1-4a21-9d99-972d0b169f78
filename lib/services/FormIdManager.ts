import { Field, Schema } from "../schema/types";
export class FormIdManager {
  /**
   * Generates IDs for all auto_generate=true fields in the form data
   */
  static async processFormData(data: any, schema: Schema): Promise<any> {
    const processedData = { ...data };

    if (schema?.schema_definition?.fields) {
      for (const field of schema.schema_definition.fields) {
        // if (field.auto_generate && field.primary_key) {
        //   // Only generate ID if it's not already set or is a temporary ID
        //   if (
        //     !processedData[field.name] ||
        //     processedData[field.name].startsWith("temp_")
        //   ) {
        //     try {
        //       processedData[field.name] = await IdGeneratorService.generateId();
        //     } catch (error) {
        //       console.error("Failed to generate ID for field:", field.name, error);
        //       // Keep the temporary ID or generate a fallback
        //       if (!processedData[field.name]) {
        //         processedData[field.name] = `temp_${Date.now()}_${Math.random()
        //           .toString(36)
        //           .substr(2, 9)}`;
        //       }
        //     }
        //   }
        // }
      }
    }

    return processedData;
  }

  /**
   * Processes relationship data to ensure proper reference IDs and generated IDs
   */
  static async processRelationshipData(
    relationshipData: any[],
    relatedSchema: Schema,
    parentId: string,
    foreignKeyField: string,
    relatedSchemas?: Record<string, Schema>
  ): Promise<any[]> {
    if (!Array.isArray(relationshipData)) {
      return [];
    }

    const processedItems = [];

    for (const item of relationshipData) {
      let processedItem = { ...item };

      // Set the foreign key (reference_id) - use the tracked field if available
      const parentIdField = foreignKeyField;
      // Check if the field has auto_populate from parent_context
      if (relatedSchema?.schema_definition?.fields) {
        const fields = relatedSchema?.schema_definition?.fields;
        for (const field of fields) {
          if (
            field.name === parentIdField &&
            field?.auto_populate?.source === "parent_context"
          ) {
            // Auto-populate the field from parent context
            processedItem[field.name] = parentId;
          }
        }
      }

      // Process nested relationships for Hasura insert format
      processedItem = await this.processNestedRelationshipsForInsert(processedItem, relatedSchema, relatedSchemas);

      // Clean up tracking field
      // delete processedItem._parentIdField;

      processedItems.push(processedItem);
    }

    return processedItems;
  }

  /**
   * Processes nested relationships in an item to format them correctly for Hasura inserts
   */
  static async processNestedRelationshipsForInsert(item: any, schema: Schema, relatedSchemas?: Record<string, Schema>): Promise<any> {
    const processedItem = { ...item };

    // Check if this schema has relationships defined
    if (schema.relationships && schema.relationships.length > 0) {
      for (const relationship of schema.relationships) {
        const relationshipData = processedItem[relationship.name];

        if (relationshipData && Array.isArray(relationshipData)) {
          // Format array relationships for Hasura nested insert
          processedItem[relationship.name] = {
            data: await Promise.all(relationshipData.map(async (relItem) => {
              const cleanItem = { ...relItem };

              // Generate new ID for items with empty IDs
              if (cleanItem.id === "" || cleanItem.id === null || cleanItem.id === undefined) {
                const { IdGeneratorService } = await import('./IdGeneratorService');
                cleanItem.id = await IdGeneratorService.generateId();
              }

              // Remove foreign key fields for nested inserts
              // Hasura automatically sets these during nested inserts
              // The foreign key field is the one that references the parent (target_field in relationship)
              if (relationship.target_field && cleanItem[relationship.target_field]) {
                delete cleanItem[relationship.target_field];
              }

              // Recursively process nested relationships
              try {
                const childSchema = relatedSchemas?.[relationship.target_component] || schema.related_schemas?.[relationship.target_component];
                if (childSchema) {
                  const processedNestedItem = await this.processNestedRelationshipsForInsert(cleanItem, childSchema, relatedSchemas);
                  return processedNestedItem;
                }
              } catch (error) {
                console.warn(`Could not fetch child schema ${relationship.target_component}:`, error);
              }

              return cleanItem;
            }))
          };
        } else if (relationshipData && typeof relationshipData === 'object' && relationshipData !== null) {
          // Handle single object relationships if needed
          const cleanItem = { ...relationshipData };
          if (cleanItem.id === "" || cleanItem.id === null || cleanItem.id === undefined) {
            delete cleanItem.id;
          }
          
          // Recursively process nested relationships for single objects too
          try {
            const childSchema = relatedSchemas?.[relationship.target_component] || schema.related_schemas?.[relationship.target_component];
            if (childSchema) {
              processedItem[relationship.name] = await this.processNestedRelationshipsForInsert(cleanItem, childSchema, relatedSchemas);
            } else {
              processedItem[relationship.name] = cleanItem;
            }
          } catch (error) {
            console.warn(`Could not fetch child schema ${relationship.target_component}:`, error);
            processedItem[relationship.name] = cleanItem;
          }
        }
      }
    }

    return processedItem;
  }

  /**
   * Gets the foreign key field name for a relationship
   */
  static getForeignKeyField(
    relatedSchema: Schema,
    relationshipName: string
  ): string {
    if (!relatedSchema?.schema_definition?.fields) {
      return `${relationshipName.split("_")[0]}_id`;
    }

    // Look for a field that has foreign_key configuration
    const foreignKeyField = relatedSchema.schema_definition.fields.find(
      (field: Field) => field.foreign_key && field.name.includes("_id")
    );

    if (foreignKeyField) {
      return foreignKeyField.name;
    }

    // Fallback to common naming patterns
    const possibleKeys = [
      `${relationshipName.split("_")[0]}_id`,
      "parent_id",
      "reference_id",
    ];

    for (const key of possibleKeys) {
      const field = relatedSchema.schema_definition.fields.find(
        (f: Field) => f.name === key
      );
      if (field) {
        return key;
      }
    }

    // Default fallback
    return `${relationshipName.split("_")[0]}_id`;
  }
}
