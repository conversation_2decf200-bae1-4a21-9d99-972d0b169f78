import { v4 as uuidv4 } from "uuid";
import { HasuraAdminClient } from "@/lib/hasura/client";
import axios from "axios";

export interface MediaItem {
  id: string;
  filename: string;
  original_filename: string;
  mime_type: string;
  file_extension: string;
  size: number;
  s3_url: string;
  width?: number;
  height?: number;
  duration?: number;
  alternative_text?: string;
  caption?: string;
  folder_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface MediaFolder {
  id: string;
  name: string;
  parent_folder_id?: string;
  path: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  asset_count?: number;
}

export interface MediaListParams {
  page: number;
  limit: number;
  folderId?: string;
  search?: string;
  mimeType?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface UploadOptions {
  folderId?: string;
  metadata?: Record<string, any>;
}

export class MediaService {
  /**
   * Upload files using the upload-new endpoint and save to media library
   */
  async uploadFilesWithNewEndpoint(
    hasuraClient: HasuraAdminClient,
    files: File[],
    options: UploadOptions = {}
  ): Promise<MediaItem[]> {
    const uploadedFiles: MediaItem[] = [];

    for (const file of files) {
      try {
        // Get upload token
        const tokenResponse = await this.getUploadToken(file.type, file.size);
        const token = tokenResponse.payload.Token;

        // Upload to S3 using upload-new endpoint
        const formData = new FormData();
        formData.append("file", file);

        const publicEndpoint = process.env.NEXT_PUBLIC_PUBLIC_ENDPOINT;
        const uploadUrl = `${publicEndpoint}/v1/studio/upload-new`;
        
        if (!uploadUrl) {
          throw new Error("Upload URL is not set");
        }

        const uploadResponse = await fetch(uploadUrl, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error("Failed to upload to S3");
        }

        const uploadResult = await uploadResponse.json();
        const documentKey = uploadResult.payload.key;

        // Generate the media URL
        const mediaUrl = `https://cdn.oasr.in/${documentKey}`;

        // Extract metadata
        const fileMetadata = await this.extractFileMetadata(file);

        // Save to database
        const mediaItem: Omit<MediaItem, "id" | "created_at" | "updated_at"> = {
          filename: file.name,
          original_filename: file.name,
          mime_type: file.type,
          file_extension: file.name.split(".").pop() || "",
          size: file.size,
          s3_url: mediaUrl,
          width: fileMetadata.width,
          height: fileMetadata.height,
          duration: fileMetadata.duration,
          alternative_text: "",
          caption: "",
          folder_id: options.folderId,
          created_by: "system", // TODO: Get from auth context
        };

        const savedItem = await this.saveMediaToDatabase(
          hasuraClient,
          mediaItem
        );
        uploadedFiles.push(savedItem);
      } catch (error) {
        console.error(`Error uploading file ${file.name}:`, error);
        throw error;
      }
    }

    return uploadedFiles;
  }

  /**
   * Get upload token from the admin API
   */
  private async getUploadToken(mimeType: string, fileSize: number): Promise<any> {
    const tokenUrl = process.env.ADMIN_API_ENDPOINT + "/v1/studio/token";
    
    // Check environment variables
    if (!process.env.ADMIN_API_ENDPOINT) {
      throw new Error("ADMIN_API_ENDPOINT environment variable is not set");
    }
    if (!process.env.HASURA_TOKEN) {
      throw new Error("HASURA_TOKEN environment variable is not set");
    }
    
    // Format file size like the working get-token endpoint
    const formatFileSize = (bytes: number) => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    try {
      const tokenResponse = await axios.post(tokenUrl, {
        AllowedMimeTypes: [mimeType],
        MaxFileSize: formatFileSize(fileSize),
      }, {
        headers: {
          Authorization: `Bearer ${process.env.HASURA_TOKEN}`,
        },
      });

      return tokenResponse.data;
    } catch (error: any) {
      console.error("Token request failed:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        error: error.response?.data,
        url: tokenUrl
      });
      throw new Error(`Failed to get upload token: ${error.response?.status} ${error.response?.data || error.message}`);
    }
  }

  private async extractFileMetadata(file: File): Promise<{
    width?: number;
    height?: number;
    duration?: number;
  }> {
    // For server-side processing, we'll return empty metadata
    // In a production environment, you might want to use a service like Sharp for images
    // or FFmpeg for videos to extract metadata after upload
    return {};
  }

  private async saveMediaToDatabase(
    hasuraClient: HasuraAdminClient,
    mediaItem: Omit<MediaItem, "id" | "created_at" | "updated_at">
  ): Promise<MediaItem> {
    const mutation = `
      mutation InsertMedia($object: cms_config_media_insert_input!) {
        insert_cms_config_media_one(object: $object) {
          id
          filename
          original_filename
          mime_type
          file_extension
          size
          s3_url
          width
          height
          duration
          alternative_text
          caption
          folder_id
          created_by
          created_at
          updated_at
        }
      }
    `;

    const variables = {
      object: {
        ...mediaItem,
        id: uuidv4().replace(/-/g, "").substring(0, 16),
      },
    };

    const result = await hasuraClient.request(mutation, variables);
    return result.insert_cms_config_media_one;
  }

  async listMedia(
    hasuraClient: HasuraAdminClient,
    params: MediaListParams
  ): Promise<{
    items: MediaItem[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page, limit, folderId, search, mimeType, sortBy, sortOrder } =
      params;
    const offset = (page - 1) * limit;

    let whereClause = "deleted_at: {_is_null: true}";
    const conditions = [];
    
    if (folderId) {
      conditions.push(`{folder_id: {_eq: "${folderId}"}}`);
    }
    
    if (search) {
      conditions.push(`{_or: [{filename: {_ilike: "%${search}%"}}, {original_filename: {_ilike: "%${search}%"}}]}`);
    }
    
    if (mimeType) {
      // Handle multiple MIME types separated by commas
      const mimeTypes = mimeType.split(',').map(type => type.trim());
      if (mimeTypes.length === 1) {
        // Single MIME type
        const singleType = mimeTypes[0];
        if (singleType.endsWith('/*')) {
          // Handle wildcard types like "image/*"
          const baseType = singleType.replace('/*', '');
          conditions.push(`{mime_type: {_ilike: "${baseType}/%"}}`);
        } else {
          // Exact MIME type
          conditions.push(`{mime_type: {_eq: "${singleType}"}}`);
        }
      } else {
        // Multiple MIME types - use _or with _in
        const mimeTypeConditions = mimeTypes.map(type => {
          if (type.endsWith('/*')) {
            // Handle wildcard types like "image/*"
            const baseType = type.replace('/*', '');
            return `{mime_type: {_ilike: "${baseType}/%"}}`;
          } else {
            // Exact MIME type
            return `{mime_type: {_eq: "${type}"}}`;
          }
        }).join(', ');
        conditions.push(`{_or: [${mimeTypeConditions}]}`);
      }
    }
    
    if (conditions.length > 0) {
      whereClause += `, _and: [${conditions.join(', ')}]`;
    }

    const query = `
      query GetMedia($limit: Int!, $offset: Int!, $orderBy: [cms_config_media_order_by!]) {
        cms_config_media(where: {${whereClause}}, limit: $limit, offset: $offset, order_by: $orderBy) {
          id
          filename
          original_filename
          mime_type
          file_extension
          size
          s3_url
          width
          height
          duration
          alternative_text
          caption
          folder_id
          created_by
          created_at
          updated_at
        }
        cms_config_media_aggregate(where: {${whereClause}}) {
          aggregate {
            count
          }
        }
      }
    `;

    const variables = {
      limit,
      offset,
      orderBy: [{ [sortBy as string]: sortOrder }],
    };

    const result = await hasuraClient.request(query, variables);
    const total = result.cms_config_media_aggregate.aggregate.count;
    const totalPages = Math.ceil(total / limit);

    return {
      items: result.cms_config_media,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async getMediaById(
    hasuraClient: HasuraAdminClient,
    id: string
  ): Promise<MediaItem | null> {
    const query = `
      query GetMediaById($id: String!) {
        cms_config_media_by_pk(id: $id) {
          id
          filename
          original_filename
          mime_type
          file_extension
          size
          s3_url
          width
          height
          duration
          alternative_text
          caption
          folder_id
          created_by
          created_at
          updated_at
        }
      }
    `;

    const result = await hasuraClient.request(query, { id });
    if (!result.cms_config_media_by_pk) return null;

    return result.cms_config_media_by_pk;
  }

  async updateMedia(
    hasuraClient: HasuraAdminClient,
    id: string,
    updates: Partial<MediaItem>
  ): Promise<MediaItem> {
    const mutation = `
      mutation UpdateMedia($id: String!, $updates: cms_config_media_set_input!) {
        update_cms_config_media_by_pk(pk_columns: {id: $id}, _set: $updates) {
          id
          filename
          original_filename
          mime_type
          file_extension
          size
          s3_url
          width
          height
          duration
          alternative_text
          caption
          folder_id
          created_by
          created_at
          updated_at
        }
      }
    `;

    const variables = {
      id,
      updates: {
        filename: updates.filename,
        alternative_text: updates.alternative_text,
        caption: updates.caption,
        folder_id: updates.folder_id,
      },
    };

    const result = await hasuraClient.request(mutation, variables);
    return result.update_cms_config_media_by_pk;
  }

  async deleteMedia(
    hasuraClient: HasuraAdminClient,
    id: string
  ): Promise<boolean> {
    // Soft delete - update deleted_at timestamp
    const mutation = `
      mutation DeleteMedia($id: String!) {
        update_cms_config_media_by_pk(pk_columns: {id: $id}, _set: {deleted_at: "now()"}) {
          id
        }
      }
    `;

    await hasuraClient.request(mutation, { id });
    return true;
  }

  async listFolders(hasuraClient: HasuraAdminClient): Promise<MediaFolder[]> {
    const query = `
      query GetFolders {
        cms_config_media_folders(order_by: {path: asc}) {
          id
          name
          parent_folder_id
          path
          created_by
          created_at
          updated_at
        }
      }
    `;

    const result = await hasuraClient.request(query);
    return result.cms_config_media_folders;
  }

  async createFolder(
    hasuraClient: HasuraAdminClient,
    name: string,
    parentFolderId?: string
  ): Promise<MediaFolder> {
    const mutation = `
      mutation CreateFolder($object: cms_config_media_folders_insert_input!) {
        insert_cms_config_media_folders_one(object: $object) {
          id
          name
          parent_folder_id
          path
          created_by
          created_at
          updated_at
        }
      }
    `;

    const path = parentFolderId ? `/${parentFolderId}/${name}` : `/${name}`;

    const variables = {
      object: {
        id: uuidv4().replace(/-/g, "").substring(0, 16),
        name,
        parent_folder_id: parentFolderId || null,
        path,
      },
    };

    const result = await hasuraClient.request(mutation, variables);
    return result.insert_cms_config_media_folders_one;
  }
}