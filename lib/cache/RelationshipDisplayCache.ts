/**
 * Global cache for relationship display values to prevent redundant API calls
 */
class RelationshipDisplayCache {
  private cache: Record<string, string> = {};
  private pendingRequests: Record<string, Promise<string>> = {};
  private batchRequests: Record<string, Promise<Record<string, string>>> = {};

  /**
   * Get multiple display values in a single batch request
   */
  async getBatchDisplayValues(
    tableName: string,
    itemValues: string[],
    relationshipDisplayField: string,
    foreignKeyColumn: string
  ): Promise<Record<string, string>> {
    const results: Record<string, string> = {};
    const uncachedValues: string[] = [];

    // Check cache first
    for (const itemValue of itemValues) {
      const cacheKey = `${tableName}:${itemValue}:${relationshipDisplayField}`;
      if (this.cache[cacheKey]) {
        results[itemValue] = this.cache[cacheKey];
      } else {
        uncachedValues.push(itemValue);
      }
    }

    // If all values are cached, return immediately
    if (uncachedValues.length === 0) {
      return results;
    }

    // Create batch request key
    const batchKey = `${tableName}:${relationshipDisplayField}:${uncachedValues.sort().join(',')}`;

    // If there's already a pending batch request for these values, wait for it
    if (await this.batchRequests[batchKey]) {
      const batchResults = await this.batchRequests[batchKey];
      return { ...results, ...batchResults };
    }

    // Create a new batch request
    const batchPromise = this.fetchBatchDisplayValues(
      tableName,
      uncachedValues,
      relationshipDisplayField,
      foreignKeyColumn
    );

    // Store the promise to prevent duplicate batch requests
    this.batchRequests[batchKey] = batchPromise;

    try {
      const batchResults = await batchPromise;

      // Cache all the results
      for (const [itemValue, displayValue] of Object.entries(batchResults)) {
        const cacheKey = `${tableName}:${itemValue}:${relationshipDisplayField}`;
        this.cache[cacheKey] = displayValue as string;
      }

      return { ...results, ...batchResults };
    } finally {
      // Clean up the pending batch request
      delete this.batchRequests[batchKey];
    }
  }

  /**
   * Get a cached display value or fetch it if not cached (fallback for single requests)
   */
  async getDisplayValue(
    tableName: string,
    itemValue: string,
    relationshipDisplayField: string,
    foreignKeyColumn: string
  ): Promise<string> {
    const batchResults = await this.getBatchDisplayValues(
      tableName,
      [itemValue],
      relationshipDisplayField,
      foreignKeyColumn
    );

    return batchResults[itemValue] || itemValue;
  }

  private async fetchBatchDisplayValues(
    tableName: string,
    itemValues: string[],
    relationshipDisplayField: string,
    foreignKeyColumn: string
  ): Promise<Record<string, string>> {
    const results: Record<string, string> = {};

    try {
      // Use the batch options API with specific IDs to fetch only what we need
      const idsParam = itemValues.join(',');
      const url = `/api/graphql/options/${tableName}?ids=${encodeURIComponent(idsParam)}&labelField=${relationshipDisplayField}&valueField=${foreignKeyColumn}&limit=${itemValues.length + 10}`;
      const response = await fetch(url);

      if (response.ok) {
        const result = await response.json();
        if (result.data && Array.isArray(result.data)) {
          // Create a map of value -> label for quick lookup
          const valueMap = new Map<string, string>();
          result.data.forEach((item: { value: string; label: string }) => {
            valueMap.set(item.value, item.label);
          });

          // Map the requested item values to their display values
          for (const itemValue of itemValues) {
            results[itemValue] = valueMap.get(itemValue) || itemValue;
          }
        }
      } else {
        console.error("Batch API response not ok:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Failed to fetch batch relationship display values:", error);
    }

    // For any values that weren't found, use the original value as fallback
    for (const itemValue of itemValues) {
      if (!results[itemValue]) {
        results[itemValue] = itemValue;
      }
    }

    return results;
  }

  /**
   * Clear the cache (useful for testing or when data changes)
   */
  clear(): void {
    this.cache = {};
    this.pendingRequests = {};
  }

  /**
   * Get cache statistics for debugging
   */
  getStats(): { cacheSize: number; pendingRequests: number } {
    return {
      cacheSize: Object.keys(this.cache).length,
      pendingRequests: Object.keys(this.pendingRequests).length
    };
  }
}

// Export a singleton instance
export const relationshipDisplayCache = new RelationshipDisplayCache();
