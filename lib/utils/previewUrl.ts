import { PreviewUrlConfig } from "@/lib/schema/types";

/**
 * Generates a preview URL from a template and record data
 * @param config - Preview URL configuration
 * @param recordData - The record data containing field values
 * @returns Generated preview URL or null if template cannot be resolved
 */
export function generatePreviewUrl(
  config: PreviewUrlConfig,
  recordData: Record<string, any>
): string | null {
  if (!config.template || !recordData) {
    return null;
  }

  try {
    let url = config.template;

    // Replace placeholders in the template with actual values
    // Supports both {field} and ${field} syntax
    url = url.replace(/\{([^}]+)\}/g, (match, fieldName) => {
      const value = recordData[fieldName];
      if (value === undefined || value === null) {
        // If any required field is missing, return null
        throw new Error(`Missing field: ${fieldName}`);
      }
      return encodeURIComponent(String(value));
    });

    // Handle ${field} syntax as well
    url = url.replace(/\$\{([^}]+)\}/g, (match, fieldName) => {
      const value = recordData[fieldName];
      if (value === undefined || value === null) {
        throw new Error(`Missing field: ${fieldName}`);
      }
      return encodeURIComponent(String(value));
    });

    // Prepend base URL if provided
    if (config.base_url) {
      // Remove trailing slash from base_url and leading slash from url if present
      const baseUrl = config.base_url.replace(/\/$/, '');
      const urlPath = url.startsWith('/') ? url : `/${url}`;
      url = `${baseUrl}${urlPath}`;
    }

    // Validate that the result is a valid URL
    try {
      new URL(url);
      return url;
    } catch {
      // If it's not a valid absolute URL, check if it's a valid relative URL
      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
        return url;
      }
      // If base_url is not provided and template doesn't start with protocol, assume https
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        return `https://${url}`;
      }
      return url;
    }
  } catch (error) {
    console.warn('Failed to generate preview URL:', error);
    return null;
  }
}

/**
 * Extracts field names used in a preview URL template
 * @param template - The URL template string
 * @returns Array of field names used in the template
 */
export function extractTemplateFields(template: string): string[] {
  const fields = new Set<string>();
  
  // Match {field} syntax
  const braceMatches = template.match(/\{([^}]+)\}/g);
  if (braceMatches) {
    braceMatches.forEach(match => {
      const fieldName = match.slice(1, -1); // Remove { and }
      fields.add(fieldName);
    });
  }

  // Match ${field} syntax
  const dollarMatches = template.match(/\$\{([^}]+)\}/g);
  if (dollarMatches) {
    dollarMatches.forEach(match => {
      const fieldName = match.slice(2, -1); // Remove ${ and }
      fields.add(fieldName);
    });
  }

  return Array.from(fields);
}

/**
 * Validates a preview URL template against available fields
 * @param config - Preview URL configuration
 * @param availableFields - Array of available field names
 * @returns Validation result with success status and error message if any
 */
export function validatePreviewUrlTemplate(
  config: PreviewUrlConfig,
  availableFields: string[]
): { isValid: boolean; error?: string; missingFields?: string[] } {
  if (!config.template) {
    return { isValid: false, error: 'Template is required' };
  }

  const requiredFields = extractTemplateFields(config.template);
  const missingFields = requiredFields.filter(field => !availableFields.includes(field));

  if (missingFields.length > 0) {
    return {
      isValid: false,
      error: `Missing fields: ${missingFields.join(', ')}`,
      missingFields
    };
  }

  return { isValid: true };
}
