import { NextRequest, NextResponse } from "next/server";
import { getHasuraAdminClient } from "@/lib/hasura/client";
import { SchemaManager } from "@/lib/schema/SchemaManager";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ componentName: string; optionId: string }> }
) {
  try {
    const resolvedParams = await params;
    const componentName = resolvedParams.componentName;
    const optionId = resolvedParams.optionId;
    const { searchParams } = new URL(request.url);
    const labelField = searchParams.get("labelField") || "name";
    const valueField = searchParams.get("valueField") || "id";

    const adminClient = getHasuraAdminClient();
    const schemaManager = new SchemaManager(adminClient);

    // Try to get the schema by component name first
    let schema = await schemaManager.getSchema(componentName);
    let hasuraTableName = componentName;

    if (schema) {
      // If we found a schema, use its Hasura table name
      hasuraTableName = schema.schema_definition?.table?.hasura_table_name ||
                       schema.schema_definition?.table?.name ||
                       componentName;
    } else {
      // If no schema found, the componentName might actually be a Hasura table name
      // Try to find a schema that has this as its hasura_table_name
      const allSchemas = await schemaManager.getAllSchemas();
      const matchingSchema = allSchemas.find(s =>
        s.schema_definition?.table?.hasura_table_name === componentName ||
        s.schema_definition?.table?.name === componentName
      );

      if (matchingSchema) {
        schema = matchingSchema;
        hasuraTableName = componentName; // Use the provided table name directly
      } else {
        throw new Error(`No schema found for component or table: ${componentName}`);
      }
    }

    let data = null;
    let queryExecuted = false;

    try {
      const query = `
          query GetSingleOption($${valueField}: String!) {
            ${hasuraTableName}_by_pk(${valueField}: $${valueField}) {
              ${valueField}
              ${labelField}
            }
          }
        `;

      const variables = {
        [valueField]: optionId,
      };

      const result = await adminClient.request(query, variables);
      data = result[`${hasuraTableName}_by_pk`];

      queryExecuted = true;
    } catch (error) {
      console.error(`Query failed for ${componentName}:`, error);
    }

    if (!queryExecuted) {
      throw new Error(`No valid table found for component: ${componentName}`);
    }

    return NextResponse.json({
      data: data ? {
        value: data[valueField],
        label: data[labelField],
      } : null,
      componentName,
      optionId,
    });
  } catch (error: any) {
    console.error("Single Option API Error:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch option",
        message: error.message,
        stack: error.stack,
      },
      { status: 500 }
    );
  }
}
