import { NextRequest, NextResponse } from "next/server";
import { getHasuraAdminClient } from "@/lib/hasura/client";
import { MediaService } from "@/lib/services/MediaService";

const mediaService = new MediaService();

export async function POST(request: NextRequest) {
  try {
    const hasuraClient = getHasuraAdminClient();
    
    // Parse the form data
    const formData = await request.formData();
    const files = formData.getAll("files") as File[];
    const folderId = formData.get("folderId") as string | null;

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: "No files provided" },
        { status: 400 }
      );
    }

    // Upload files using the new endpoint and save to media library
    const uploadedMediaItems = await mediaService.uploadFilesWithNewEndpoint(
      hasuraClient,
      files,
      {
        folderId: folderId || undefined,
      }
    );

    // Return the uploaded media items with their URLs
    const result = uploadedMediaItems.map(item => ({
      id: item.id,
      filename: item.filename,
      url: item.s3_url,
      mimeType: item.mime_type,
      size: item.size,
      width: item.width,
      height: item.height,
    }));

    return NextResponse.json({
      success: true,
      data: result,
    });

  } catch (error: any) {
    console.error("File upload error:", error);
    return NextResponse.json(
      { 
        error: "Upload failed", 
        message: error.message 
      },
      { status: 500 }
    );
  }
}
