import { NextRequest, NextResponse } from "next/server";
import { getHasuraAdminClient } from "@/lib/hasura/client";
import { MediaService } from "@/lib/services/MediaService";
import axios from "axios";

const mediaService = new MediaService();

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export async function POST(request: NextRequest) {
  try {
    // const { filename, contentType, folderId, size } = await request.json();

    const requestBody = await request.json();

    const tokenUrl = process.env.ADMIN_API_ENDPOINT + "/v1/studio/token";

    console.log("tokenUrl", tokenUrl);

    const tokenResponse = await axios.post(tokenUrl, {
      AllowedMimeTypes: requestBody.AllowedMimeTypes,
      MaxFileSize: formatFileSize(requestBody.MaxFileSize),
    },{
      headers: {
        Authorization: `Bearer ${process.env.HASURA_TOKEN}`,
      },
    });

    return NextResponse.json(tokenResponse.data);

  } catch (error: any) {
    console.error("Signed URL generation error:", error);
    return NextResponse.json(
      { error: "Internal server error", message: error.message },
      { status: 500 }
    );
  }
}
