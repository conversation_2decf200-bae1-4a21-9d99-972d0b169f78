import { NextRequest, NextResponse } from "next/server";
import { getHasuraAdminClient } from "@/lib/hasura/client";
import { SchemaManager } from "@/lib/schema/SchemaManager";
import { FormGenerator } from "@/lib/schema/FormGenerator";
import { CacheManager } from "@/lib/schema/CacheManager";
import { IdGeneratorService } from "@/lib/services/IdGeneratorService";
import { Field, Relationship, Schema } from "@/lib/schema/types";

const cacheManager = new CacheManager(
  process.env.REDIS_URL,
  process.env.CACHE === "true"
);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ schemaName: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const entityId = searchParams.get("entityId");
    const parentId = searchParams.get("parentId");

    const hasuraClient = getHasuraAdminClient();
    const schemaManager = new SchemaManager(hasuraClient, cacheManager);
    const generator = new FormGenerator(schemaManager);

    const resolvedParams = await params;

    // Prepare context data for auto-population
    const contextData = parentId ? { parentId } : undefined;

    const formConfig = await generator.generateFormConfig(
      resolvedParams.schemaName,
      entityId || undefined,
      contextData
    );

    return NextResponse.json(formConfig);
  } catch (error: any) {
    console.error("Form API error:", error);
    return NextResponse.json(
      { error: "Internal server error", message: error.message },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ schemaName: string }> }
) {
  const startTime = Date.now();
  try {
    const { data, entityId } = await request.json();
    console.log(`[Timing] Request parsing: ${Date.now() - startTime}ms`);

    const clientStartTime = Date.now();
    const hasuraClient = getHasuraAdminClient();
    const schemaManager = new SchemaManager(hasuraClient, cacheManager);
    console.log(`[Timing] Client initialization: ${Date.now() - clientStartTime}ms`);

    const paramsStartTime = Date.now();
    const resolvedParams = await params;
    console.log(`[Timing] Params resolution: ${Date.now() - paramsStartTime}ms`);

    const schemaStartTime = Date.now();
    const schema = await schemaManager.getSchema(resolvedParams.schemaName, "latest", true);
    console.log(`[Timing] Schema fetch: ${Date.now() - schemaStartTime}ms`);

    if (!schema) {
      return NextResponse.json({ error: "Schema not found" }, { status: 404 });
    }

    let result;

    if (schema.relationships && schema.relationships.length > 0) {
      // Use relationship-aware mutation
      const operationName = entityId
        ? `update${pascalCase(schema.name)}WithRelations`
        : `create${pascalCase(schema.name)}WithRelations`;

      const mutationStartTime = Date.now();
      result = await executeComplexMutation(
        hasuraClient,
        schema,
        data,
        entityId
      );
      console.log(`[Timing] Complex mutation execution: ${Date.now() - mutationStartTime}ms`);
    } else {
      // Use standard mutation
      const tableName = getTableName(schema, null);

      if (entityId) {
        // Update
        const primaryKey = schema.schema_definition.fields.find(
          (f) => f.primary_key
        );
        if (!primaryKey) throw new Error("No primary key found");

        const mutation = `
          mutation UpdateEntity($id: String!, $data: ${tableName}_set_input!) {
            update_${tableName}_by_pk(
              pk_columns: {${primaryKey.name}: $id},
              _set: $data
            ) {
              ${primaryKey.name}
            }
          }
        `;

        const updateStartTime = Date.now();
        const response = await hasuraClient.request(mutation, {
          id: entityId,
          data: data,
        });
        console.log(`[Timing] Update mutation: ${Date.now() - updateStartTime}ms`);

        result = response[`update_${tableName}_by_pk`];
      } else {
        // Create

        const mutation = `
          mutation CreateEntity($data: ${tableName}_insert_input!) {
            insert_${tableName}_one(object: $data) {
              id
            }
          }
        `;
        
        const idGenStartTime = Date.now();
        const id = await IdGeneratorService.generateId();
        data.id = id;
        console.log(`[Timing] ID generation: ${Date.now() - idGenStartTime}ms`);

        const createStartTime = Date.now();
        const response = await hasuraClient.request(mutation, {
          data: data,
        });
        console.log(`[Timing] Create mutation: ${Date.now() - createStartTime}ms`);

        result = response[`insert_${tableName}_one`];
      }
    }

    console.log(`[Timing] Total POST execution: ${Date.now() - startTime}ms`);
    return NextResponse.json(result);
  } catch (error: any) {
    console.error("Form submission error:", error);
    console.log(`[Timing] Total POST execution (with error): ${Date.now() - startTime}ms`);
    return NextResponse.json(
      { error: "Internal server error", message: error.message },
      { status: 500 }
    );
  }
}

async function executeComplexMutation(
  hasuraClient: any,
  schema: Schema,
  data: any,
  entityId?: string
) {
  // This is a simplified version - in production, you would need to
  // handle the complex relationships properly
  const tableName = getTableName(schema, null);

  // Extract main entity data and relationship data
  const mainData: any = {};
  const relationshipData: any = {};

  // First, identify all relationship names to exclude them from main data
  const relationshipNames = new Set(
    (schema.relationships || []).map((rel: Relationship) => rel.name)
  );

  // Extract main entity data (only fields that are NOT relationships)
  for (const field of schema.schema_definition.fields) {
    if (data[field.name] !== undefined && !relationshipNames.has(field.name)) {
      // Only include if it's not a relationship field
      mainData[field.name] = data[field.name];
    }
  }

  // Extract relationship data
  for (const rel of schema.relationships || []) {
    if (data[rel.name] !== undefined) {
      relationshipData[rel.name] = data[rel.name];
    }
    if (rel.type === "many-to-one") {
      // For many-to-one relationships, extract just the ID from the related object
      const relatedData = data[rel.name];
      if (relatedData && typeof relatedData === 'object' && relatedData.id) {
        // If it's an object with an ID, use just the ID
        mainData[rel.source_field] = relatedData.id;
      } else if (relatedData && typeof relatedData === 'string') {
        // If it's already a string ID, use it directly
        mainData[rel.source_field] = relatedData;
      } else if (data[rel.source_field]) {
        // Fall back to the direct foreign key field value
        mainData[rel.source_field] = data[rel.source_field];
      }
    }
  }
  // Start transaction-like operation
  const results: any = {};

  if (entityId) {
    // Update main entity
    const primaryKey = schema.schema_definition.fields.find(
      (f: Field) => f.primary_key
    );
    if (!primaryKey) throw new Error("No primary key found");

    const updateMutation = `
      mutation UpdateEntity($id: String!, $data: ${tableName}_set_input!) {
        update_${tableName}_by_pk(
          pk_columns: {${primaryKey.name}: $id},
          _set: $data
        ) {
          ${primaryKey.name}
        }
      }
    `;

    const response = await hasuraClient.request(updateMutation, {
      id: entityId,
      data: mainData,
    });

    results.main = response[`update_${tableName}_by_pk`];

    // Handle relationship updates recursively with deletion support
    for (const relName in relationshipData) {
      const rel = schema.relationships?.find(
        (r: Relationship) => r.name === relName
      );
      if (rel && rel.type === "one-to-many" && Array.isArray(relationshipData[relName])) {
        // For updates, we need to handle deletions, existing items, and new items
        await updateRelationshipItemsWithDeletion(
          hasuraClient,
          schema,
          rel,
          relationshipData[relName],
          entityId
        );
      } else if (rel && rel.type === "one-to-one" && relationshipData[relName]) {
        await updateRelationshipItemsWithDeletion(
          hasuraClient,
          schema,
          rel,
          [relationshipData[relName]], // Wrap in array for consistency
          entityId
        );
      }
    }
  } else {
    // Create main entity
    const createMutation = `
      mutation CreateEntity($data: ${tableName}_insert_input!) {
        insert_${tableName}_one(object: $data) {
          id
        }
      }
    `;

    const id = await IdGeneratorService.generateId();
    mainData.id = id;

    const response = await hasuraClient.request(createMutation, {
      data: mainData,
    });

    results.main = response[`insert_${tableName}_one`];
    const parentId = results.main.id;

    // Handle relationship creation
    for (const relName in relationshipData) {
      const rel = schema.relationships?.find(
        (r: Relationship) => r.name === relName
      );

      if (
        rel &&
        rel.type === "one-to-many" &&
        Array.isArray(relationshipData[relName])
      ) {
        await createRelationshipItems(
          hasuraClient,
          schema,
          rel,
          relationshipData[relName],
          parentId
        );
      } else if (rel && rel.type === "one-to-one" && relationshipData[relName]) {
        // Handle one-to-one relationships
        await createRelationshipItems(
          hasuraClient,
          schema,
          rel,
          [relationshipData[relName]], // Wrap in array for consistency
          parentId
        );
      }
    }
  }

  return results.main;
}

async function createRelationshipItems(
  hasuraClient: any,
  mainSchema: Schema,
  relationship: Relationship,
  items: any[],
  parentId: string
) {
  if (!items || items.length === 0) return;

  const targetTableName = getTableName(mainSchema, relationship.target_component);
  const targetSchema = mainSchema.related_schemas?.[relationship.target_component];

  if (!targetSchema) {
    throw new Error(`Target schema not found for component: ${relationship.target_component}`);
  }

  // Process each item to handle nested relationships
  const processedResults = [];

  for (const item of items) {
    const processedItem = { ...item };

    // Extract nested relationship data from this item
    const nestedRelationshipData: any = {};
    const mainItemData: any = {};

    // Identify nested relationships in the target schema
    const nestedRelationshipNames = new Set(
      (targetSchema.relationships || []).map((rel: Relationship) => rel.name)
    );

    // Separate main item data from nested relationship data
    for (const field of targetSchema.schema_definition.fields) {
      if (processedItem[field.name] !== undefined && !nestedRelationshipNames.has(field.name)) {
        mainItemData[field.name] = processedItem[field.name];
      }
    }

    // Extract nested relationship data
    for (const nestedRel of targetSchema.relationships || []) {
      if (processedItem[nestedRel.name] !== undefined) {
        nestedRelationshipData[nestedRel.name] = processedItem[nestedRel.name];
      }
    }

    // Set the foreign key based on relationship configuration - ALWAYS override with correct parent ID
    if (relationship.target_field) {
      mainItemData[relationship.target_field] = parentId;
    }
    // Remove tracking fields after processing
    // delete mainItemData._uniqueId;

    // Generate ID for the item if needed
    if (!mainItemData.id) {
      mainItemData.id = await IdGeneratorService.generateId();
    }

    // Get primary key for the target schema
    const targetPrimaryKey = targetSchema.schema_definition.fields.find(
      (f: any) => f.primary_key
    );
    if (!targetPrimaryKey) throw new Error(`No primary key found for ${targetTableName}`);

    // Create the main item first
    const insertMutation = `
      mutation InsertRelationshipItem($item: ${targetTableName}_insert_input!) {
        insert_${targetTableName}_one(object: $item) {
          ${targetPrimaryKey.name}
        }
      }
    `;

    try {
      const response = await hasuraClient.request(insertMutation, {
        item: mainItemData,
      });

      const createdItem = response[`insert_${targetTableName}_one`];
      const createdItemId = createdItem[targetPrimaryKey.name];
      processedResults.push(createdItem);

      // Now handle nested relationships recursively
      for (const nestedRelName in nestedRelationshipData) {
        const nestedRel = targetSchema.relationships?.find(
          (r: Relationship) => r.name === nestedRelName
        );

        if (nestedRel && nestedRel.type === "one-to-many" && Array.isArray(nestedRelationshipData[nestedRelName])) {
          await createRelationshipItems(
            hasuraClient,
            mainSchema,
            nestedRel,
            nestedRelationshipData[nestedRelName],
            createdItemId
          );
        } else if (nestedRel && nestedRel.type === "one-to-one" && nestedRelationshipData[nestedRelName]) {
          // Handle one-to-one relationships
          await createRelationshipItems(
            hasuraClient,
            mainSchema,
            nestedRel,
            [nestedRelationshipData[nestedRelName]], // Wrap in array for consistency
            createdItemId
          );
        }
      }
    } catch (error: any) {
      throw new Error(
        `Failed to create ${relationship.name} item: ${error.message}`
      );
    }
  }

  return processedResults;
}

async function updateRelationshipItemsWithDeletion(
  hasuraClient: any,
  mainSchema: Schema,
  relationship: Relationship,
  currentItems: any[],
  parentId: string
) {
  // First, fetch the existing items to compare for deletions
  const targetTableName = getTableName(mainSchema, relationship.target_component);
  const targetSchema = mainSchema.related_schemas?.[relationship.target_component];

  if (!targetSchema) {
    throw new Error(`Target schema not found for component: ${relationship.target_component}`);
  }

  // Fetch existing items for this relationship
  const primaryKey = targetSchema.schema_definition.fields.find((f: any) => f.primary_key);
  if (!primaryKey) throw new Error(`No primary key found for ${targetTableName}`);

  const fetchQuery = `
    query FetchExistingItems($parentId: String!) {
      ${targetTableName}(where: {${relationship.target_field}: {_eq: $parentId}}) {
        ${primaryKey.name}
      }
    }
  `;

  const existingResponse = await hasuraClient.query({
    query: fetchQuery,
    variables: { parentId }
  });

  const existingItems = existingResponse[targetTableName] || [];
  const currentIds = new Set(
    currentItems
      .filter(item => item && item.id && item.id.trim() !== '' && !item.id.startsWith('temp_') && !item.id.startsWith('item_'))
      .map(item => item.id)
  );

  // Find items to delete (exist in database but not in current form data)
  const itemsToDelete = existingItems.filter((item: any) => !currentIds.has(item[primaryKey.name]));

  // Delete removed items
  for (const item of itemsToDelete) {
    const deleteMutation = `
      mutation DeleteRelationshipItem($id: String!) {
        delete_${targetTableName}_by_pk(id: $id) {
          ${primaryKey.name}
        }
      }
    `;

    try {
      await hasuraClient.request(deleteMutation, { id: item[primaryKey.name] });
    } catch (error: any) {
      throw new Error(`Failed to delete ${relationship.name} item: ${error.message}`);
    }
  }

  // Now process updates and creates using the existing function
  await updateRelationshipItems(hasuraClient, mainSchema, relationship, currentItems, parentId);
}

async function updateRelationshipItems(
  hasuraClient: any,
  mainSchema: Schema,
  relationship: Relationship,
  items: any[],
  parentId: string
) {
  if (!items || items.length === 0) return;

  const targetTableName = getTableName(mainSchema, relationship.target_component);
  const targetSchema = mainSchema.related_schemas?.[relationship.target_component];

  if (!targetSchema) {
    throw new Error(`Target schema not found for component: ${relationship.target_component}`);
  }

  // Process each item to handle nested relationships
  for (const item of items) {
    const processedItem = { ...item };

    // Extract nested relationship data from this item
    const nestedRelationshipData: any = {};
    const mainItemData: any = {};

    // Identify nested relationships in the target schema
    const nestedRelationshipNames = new Set(
      (targetSchema.relationships || []).map((rel: Relationship) => rel.name)
    );

    // Separate main item data from nested relationship data with proper filtering
    for (const field of targetSchema.schema_definition.fields) {
      if (processedItem[field.name] !== undefined && !nestedRelationshipNames.has(field.name)) {
        // Always include the primary key (id) field for identifying existing items
        if (field.primary_key) {
          mainItemData[field.name] = processedItem[field.name];
        }
        // Apply filtering logic for other fields
        else if (field && !field.auto_generate) {
          // Skip fields that are hidden or auto-populated
          if (field.ui_config?.hidden || field.auto_populate) {
            continue;
          }
          // Skip null values for required fields to avoid GraphQL validation errors
          if (processedItem[field.name] === null && field.required) {
            continue;
          }
          // Only include non-null values or explicitly allow null for optional fields
          if (processedItem[field.name] !== null || !field.required) {
            mainItemData[field.name] = processedItem[field.name];
          }
        }
      }
    }

    // Extract nested relationship data
    for (const nestedRel of targetSchema.relationships || []) {
      if (processedItem[nestedRel.name] !== undefined) {
        nestedRelationshipData[nestedRel.name] = processedItem[nestedRel.name];
      }
    }

    // Remove tracking fields
    // delete mainItemData._uniqueId;

    // Determine if this is an update or create based on whether the item has a valid ID
    if (mainItemData.id && mainItemData.id.trim() !== '' && !mainItemData.id.startsWith('temp_') && !mainItemData.id.startsWith('item_')) {
      // Update existing item
      const primaryKey = targetSchema.schema_definition.fields.find(
        (f: any) => f.primary_key
      );
      if (!primaryKey) throw new Error(`No primary key found for ${targetTableName}`);

      const updateMutation = `
        mutation UpdateRelationshipItem($id: String!, $data: ${targetTableName}_set_input!) {
          update_${targetTableName}_by_pk(
            pk_columns: {${primaryKey.name}: $id},
            _set: $data
          ) {
            ${primaryKey.name}
          }
        }
      `;

      try {
        const { id, ...updateData } = mainItemData;
        const response = await hasuraClient.request(updateMutation, {
          id: id,
          data: updateData,
        });

        const updatedItem = response[`update_${targetTableName}_by_pk`];
        const updatedItemId = updatedItem[primaryKey.name];

        // Handle nested relationships recursively
        for (const nestedRelName in nestedRelationshipData) {
          const nestedRel = targetSchema.relationships?.find(
            (r: Relationship) => r.name === nestedRelName
          );

          if (nestedRel && nestedRel.type === "one-to-many" && Array.isArray(nestedRelationshipData[nestedRelName])) {
            await updateRelationshipItemsWithDeletion(
              hasuraClient,
              mainSchema,
              nestedRel,
              nestedRelationshipData[nestedRelName],
              updatedItemId
            );
          } else if (nestedRel && nestedRel.type === "one-to-one" && nestedRelationshipData[nestedRelName]) {
            await updateRelationshipItemsWithDeletion(
              hasuraClient,
              mainSchema,
              nestedRel,
              [nestedRelationshipData[nestedRelName]],
              updatedItemId
            );
          }
        }
      } catch (error: any) {
        throw new Error(
          `Failed to update ${relationship.name} item: ${error.message}`
        );
      }
    } else {
      // Create new item (same logic as createRelationshipItems)
      // Set the foreign key based on relationship configuration - ALWAYS override with correct parent ID
      if (relationship.target_field) {
        // Always set the correct parent ID
        mainItemData[relationship.target_field] = parentId;
      }
      // Generate new ID
      mainItemData.id = await IdGeneratorService.generateId();

      // Get primary key for the target schema
      const targetPrimaryKey = targetSchema.schema_definition.fields.find(
        (f: any) => f.primary_key
      );
      if (!targetPrimaryKey) throw new Error(`No primary key found for ${targetTableName}`);

      const insertMutation = `
        mutation InsertRelationshipItem($item: ${targetTableName}_insert_input!) {
          insert_${targetTableName}_one(object: $item) {
            ${targetPrimaryKey.name}
          }
        }
      `;

      try {
        const response = await hasuraClient.request(insertMutation, {
          item: mainItemData,
        });

        const createdItem = response[`insert_${targetTableName}_one`];
        const createdItemId = createdItem[targetPrimaryKey.name];

        // Handle nested relationships recursively
        for (const nestedRelName in nestedRelationshipData) {
          const nestedRel = targetSchema.relationships?.find(
            (r: Relationship) => r.name === nestedRelName
          );

          if (nestedRel && nestedRel.type === "one-to-many" && Array.isArray(nestedRelationshipData[nestedRelName])) {
            await updateRelationshipItemsWithDeletion(
              hasuraClient,
              mainSchema,
              nestedRel,
              nestedRelationshipData[nestedRelName],
              createdItemId
            );
          } else if (nestedRel && nestedRel.type === "one-to-one" && nestedRelationshipData[nestedRelName]) {
            await updateRelationshipItemsWithDeletion(
              hasuraClient,
              mainSchema,
              nestedRel,
              [nestedRelationshipData[nestedRelName]],
              createdItemId
            );
          }
        }
      } catch (error: any) {
        throw new Error(
          `Failed to create ${relationship.name} item: ${error.message}`
        );
      }
    }
  }
}

function getTableName(
  schema: Schema | null,
  targetComponent: string | null
): string {
  if (targetComponent) {
    const targetSchema = schema?.related_schemas?.[targetComponent];
    if (!targetSchema) throw new Error("Target schema not found");
    return (
      targetSchema.schema_definition.table?.hasura_table_name ||
      targetSchema.schema_definition.table?.name ||
      targetComponent
    );
  }

  if (!schema) throw new Error("Schema not found");

  return (
    schema.schema_definition.table?.hasura_table_name ||
    schema.schema_definition.table?.name ||
    `${schema.name}s`
  );
}

function pascalCase(str: string): string {
  return str.replace(/(^\w|_\w)/g, (match) =>
    match.replace("_", "").toUpperCase()
  );
}