import React from "react";
import { useFormContext } from "react-hook-form";
import { ExternalLink, Copy, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { generatePreviewUrl } from "@/lib/utils/previewUrl";
import { PreviewUrlConfig } from "@/lib/schema/types";
import { useState } from "react";

interface PreviewUrlWidgetProps {
  config: PreviewUrlConfig;
  label?: string;
  className?: string;
}

export const PreviewUrlWidget: React.FC<PreviewUrlWidgetProps> = ({
  config,
  label,
  className = "",
}) => {
  const { watch } = useFormContext();
  const [copied, setCopied] = useState(false);
  
  // Watch all form values to regenerate URL when any field changes
  const formData = watch();
  
  // Generate the preview URL
  const previewUrl = generatePreviewUrl(config, formData);
  
  const handleCopyUrl = async () => {
    if (previewUrl) {
      try {
        await navigator.clipboard.writeText(previewUrl);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy URL:', error);
      }
    }
  };

  const handleOpenUrl = () => {
    if (previewUrl) {
      window.open(previewUrl, '_blank', 'noopener,noreferrer');
    }
  };

  if (!previewUrl) {
    return (
      <div className={`space-y-2 ${className}`}>
        <Label className="text-sm font-medium text-muted-foreground">
          {label || config.label || "Preview URL"}
        </Label>
        <Card className="border-dashed">
          <CardContent className="p-4">
            <p className="text-sm text-muted-foreground text-center">
              Preview URL will be generated when all required fields are filled
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <Label className="text-sm font-medium">
        {label || config.label || "Preview URL"}
      </Label>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <div className="flex-1 min-w-0">
              <a className="text-sm font-mono text-foreground truncate" title={previewUrl} href={previewUrl} target="_blank" rel="noopener noreferrer">
                {previewUrl}
              </a>
            </div>
            <div className="flex items-center space-x-1">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleCopyUrl}
                className="h-8 w-8 p-0"
                title="Copy URL"
              >
                {copied ? (
                  <Check className="h-3 w-3 text-green-600" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleOpenUrl}
                className="h-8 w-8 p-0"
                title="Open URL in new tab"
              >
                <ExternalLink className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
