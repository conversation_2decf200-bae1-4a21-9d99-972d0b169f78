"use client";

import React, { useState, useEffect } from "react";
import { X, Image as ImageIcon, File, FolderOpen } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MediaLibrarySelector } from "./MediaLibrarySelector";
import { MediaItem } from "@/lib/services/MediaService";

// Simple interface for display data derived from URLs
interface FileDisplayData {
  filename: string;
  url: string;
  mimeType: string;
  size?: number;
}

interface FileUploadWidgetProps {
  value?: string | string[] | null; // URLs - can be string, array of strings, empty string, empty array, or null
  onChange?: (value: string | string[] | null) => void; // URLs - returns string, array, empty string, or empty array
  multiple?: boolean;
  allowedTypes?: string[];
  maxFiles?: number;
  maxFileSize?: number; // in bytes
  required?: boolean;
  disabled?: boolean;
  label?: string;
}

export function FileUploadWidget({
  value,
  onChange,
  multiple = false,
  allowedTypes = ["image/*", "application/pdf"],
  maxFiles = 1,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  required = false,
  disabled = false,
  label = "Upload files",
}: FileUploadWidgetProps) {
  const [showMediaLibrary, setShowMediaLibrary] = useState(false);

  // State for display data derived from URLs
  const [displayData, setDisplayData] = useState<FileDisplayData[]>([]);

  // Helper function to create display data from URL
  const createDisplayDataFromUrl = (url: string): FileDisplayData => {
    const urlParts = url.split('/');
    const filename = urlParts[urlParts.length - 1] || 'Unknown file';
    
    // Try to determine MIME type from file extension
    const extension = filename.split('.').pop()?.toLowerCase();
    let mimeType = 'application/octet-stream';
    
    if (extension) {
      const mimeTypes: Record<string, string> = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'webp': 'image/webp',
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'ppt': 'application/vnd.ms-powerpoint',
        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'mp4': 'video/mp4',
        'avi': 'video/x-msvideo',
        'mov': 'video/quicktime',
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'zip': 'application/zip',
        'rar': 'application/x-rar-compressed',
      };
      mimeType = mimeTypes[extension] || 'application/octet-stream';
    }

    return {
      filename,
      url,
      mimeType,
    };
  };

  // Update display data when value changes
  useEffect(() => {
    if (!value) {
      setDisplayData([]);
      return;
    }

    const urls = Array.isArray(value) ? value : [value];
    const validUrls = urls.filter(url => url && url.trim() !== "");
    const fileDisplayData = validUrls.map(createDisplayDataFromUrl);
    setDisplayData(fileDisplayData);
  }, [value]);

  const removeFile = (fileUrl: string) => {
    if (!onChange) return;

    if (multiple) {
      const currentUrls = Array.isArray(value) ? value : value ? [value] : [];
      const newUrls = currentUrls.filter((url) => url !== fileUrl);
      // For multiple files, return empty array or null based on whether there are remaining files
      onChange(newUrls.length > 0 ? newUrls : []);
    } else {
      // For single file, return empty string instead of null to avoid Hasura validation errors
      onChange("");
    }
  };

  const handleMediaLibrarySelect = (selectedItems: MediaItem[]) => {
    if (!onChange) return;

    const selectedUrls = selectedItems.map(item => item.s3_url);

    if (multiple) {
      // Combine existing URLs with new URLs
      const existingUrls = Array.isArray(value) ? value : value ? [value] : [];
      const newUrls = [...existingUrls, ...selectedUrls];
      onChange(newUrls);
    } else {
      // For single file, just use the first selected URL
      onChange(selectedUrls[0] || "");
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith("image/")) return <ImageIcon className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  return (
    <div className="space-y-4">
      {/* Upload Button */}
      {(!multiple || displayData.length < maxFiles) && (
        <Button
          type="button"
          variant="outline"
          onClick={() => setShowMediaLibrary(true)}
          disabled={disabled}
          className="w-32 h-32 flex flex-col items-center justify-center gap-2"
        >
          <FolderOpen className="w-6 h-6" />
          <span className="text-sm font-medium">Upload File</span>
        </Button>
      )}

      {/* Uploaded Files */}
      {displayData.length > 0 && (
        <div className="space-y-2">
          {displayData.map((file) => (
            <Card key={file.url} className="border border-gray-200">
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                      {(file.mimeType.startsWith("image/") || file.mimeType === "image/svg+xml" || file.filename.toLowerCase().endsWith('.svg')) ? (
                        <img
                          src={file.url}
                          alt={file.filename}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        getFileIcon(file.mimeType)
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.filename}
                      </p>
                      <p className="text-xs text-gray-500">
                        {file.size ? formatFileSize(file.size) : "Unknown size"}
                      </p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(file.url)}
                    disabled={disabled}
                    className="text-gray-400 hover:text-red-600 hover:bg-red-50"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Media Library Selector */}
      <MediaLibrarySelector
        isOpen={showMediaLibrary}
        onClose={() => setShowMediaLibrary(false)}
        onSelect={handleMediaLibrarySelect}
        multiple={multiple}
        allowedTypes={allowedTypes}
        maxFiles={maxFiles}
      />
    </div>
  );
}